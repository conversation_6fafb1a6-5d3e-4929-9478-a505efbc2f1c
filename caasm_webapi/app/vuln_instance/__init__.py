from caasm_web_tool.patch.router import DefaultRouter

from caasm_webapi.app.vuln_instance.views import (
    VulnInstanceQueryAttrAPIView,
    VulnLevelDistributeAPIView,
    VulnInstanceAggeWithVulnNameAPIView,
    VulnInstanceCVSSAPIView,
    VulnTotalViewAPIView,
    vulnDistributionViewAPIView,
    VulnPriorityTrendAPIView,
    VulnTraitsStatisticsAPIView,
    VulnAssetDisplayValueStatisticsAPIView,
    VulnPackageNameStatisticsAPIView,
    VulnExternalAssetStatisticsAPIView,
    VulnBusinessSystemStatisticsAPIView,
)


vuln_instance = DefaultRouter("vul_instance_unique")


vuln_instance.join_path("filterQuery/", VulnInstanceQueryAttrAPIView, name="过滤筛选器")
vuln_instance.join_path("levelDistribution/", VulnLevelDistributeAPIView, name="漏洞等级分布")
vuln_instance.join_path("vulnAggsWithVulnName/", VulnInstanceAggeWithVulnNameAPIView, name="漏洞资产聚合")
vuln_instance.join_path("cvssInformation/", VulnInstanceCVSSAPIView, name="cvss定义")

# 总览
vuln_instance.join_path("vulnTotalView/", VulnTotalViewAPIView, name="漏洞总览")
vuln_instance.join_path("vulnDistributionView/", vulnDistributionViewAPIView, name="漏洞分布")
vuln_instance.join_path("priorityTrend/", VulnPriorityTrendAPIView, name="漏洞优先级趋势统计")
vuln_instance.join_path("traitsStatistics/", VulnTraitsStatisticsAPIView, name="漏洞特征统计")
vuln_instance.join_path("assetIPStatistics/", VulnAssetDisplayValueStatisticsAPIView, name="资产显示值统计")
vuln_instance.join_path("packageNameStatistics/", VulnPackageNameStatisticsAPIView, name="软件包名称统计")
vuln_instance.join_path("externalAssetStatistics/", VulnExternalAssetStatisticsAPIView, name="外网资产统计")
vuln_instance.join_path("businessSystemStatistics/", VulnBusinessSystemStatisticsAPIView, name="业务系统统计")
