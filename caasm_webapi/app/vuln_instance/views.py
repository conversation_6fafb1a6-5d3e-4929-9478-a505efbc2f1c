import yaml
import os

from rest_framework.request import Request
from rest_framework.views import APIView


from caasm_aql.queriers.es import ESLogicalGroupQuerier
from caasm_render.query.base import BaseQuery
from caasm_service.entity.category_view import QueryFilterEntity
from caasm_webapi.app.query_engine.requests.entity import QueryAttributeRequest
from caasm_webapi.app.query_engine.views.entity import QueryFilterRenderBase
from caasm_meta_data.constants import Category
from caasm_vul.enums import (
    VulResponseStatus,
    VUL_RESPONSE_STATUS_MAPPING,
    VulSeverity,
    VUL_SEVERITY_MAPPING,
)
from caasm_aql.query_filters.runtime import query_filter_manager
from caasm_webapi.app.vuln_instance.requests import (
    VulnAggsWithVulnNameRequest,
    VulnPriorityTrendRequest,
    VulnQueryAttributeRequest,
)
from caasm_webapi.app.vuln_instance.serializers import (
    VulnQueryAttributeRequestSerializer,
    VulnLevelDistributionRequestSerializer,
    VulnInstanceAggeWithVulnNameRequestSerializer,
    VulnPriorityTrendRequestSerializer,
)
from caasm_service.runtime import entity_service
from caasm_webapi.util.response import build_failed, build_success, ResponseCode
from caasm_config.config import caasm_config
from caasm_service.schema.runtime import query_filter_schema
from caasm_tool.util import DATETIME_FORMAT


class VulnInstanceQueryAttrAPIView(QueryFilterRenderBase, BaseQuery, APIView):
    def post(self, request: Request):
        serializer = VulnQueryAttributeRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(code=ResponseCode.REQUEST_ERROR)
        result = []
        req: VulnQueryAttributeRequest = serializer.save()
        global_asql = self._get_asql(req.asql, req.category, req.date, req.filters, req.entity_type)
        query_filters = []
        query_filters.append(req.attribute_filter)
        if req.venn_filter:
            query_filters.append(req.venn_filter)
        for attribute_filter in req.attribute_filters:
            filter_result = query_filter_manager.calculate_attribute_filter(
                global_asql,
                Category.VUL_INSTANCE_UNIQUE,
                req.date,
                venn_filter_entity=req.venn_filter,
                attribute_filter_entity=attribute_filter,
                entity_type=req.entity_type,
                init=False,
                offset=req.page_size * req.page_index,
                limit=req.page_size,
                query_filter_id=req.query_filter_id,
            )
            result.append(filter_result.as_dict())
        return build_success(data=result)


class VulnLevelDistributeAPIView(QueryFilterRenderBase, BaseQuery, APIView):
    _filter_vuln_response_status = [VulResponseStatus.FIXED, VulResponseStatus.TOLERABLE, VulResponseStatus.ACCEPTED]
    _vuln_label = [
        VUL_SEVERITY_MAPPING[VulSeverity.CRITICAL],
        VUL_SEVERITY_MAPPING[VulSeverity.HIGH],
        VUL_SEVERITY_MAPPING[VulSeverity.MEDIUM],
        VUL_SEVERITY_MAPPING[VulSeverity.LOW],
    ]

    def post(self, request: Request):
        serializer = VulnLevelDistributionRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(code=ResponseCode.REQUEST_ERROR)
        req: QueryAttributeRequest = serializer.save()
        global_asql = self._get_asql(req.asql, req.category, req.date, req.filters, req.entity_type)
        querier = ESLogicalGroupQuerier(self.find_field_to_mapper(Category.VUL_INSTANCE_UNIQUE, None))
        global_condition, _, _ = querier.parse_aql(Category.VUL_INSTANCE_UNIQUE, global_asql)
        count = entity_service.get_count(Category.VUL_INSTANCE_UNIQUE, None, condition=global_condition)
        result = []
        # 内网可访问的
        internal_asql = "$.internet_mapping.exposure = '内网可访问'"
        internal_result = query_filter_manager.calculate_attribute_filter(
            internal_asql,
            Category.VUL_INSTANCE_UNIQUE,
            req.date,
            venn_filter_entity=req.venn_filter,
            attribute_filter_entity=req.attribute_filter,
            entity_type=req.entity_type,
            init=False,
            offset=req.page_size * req.page_index,
            limit=req.page_size,
            query_filter_id=req.query_filter_id,
        )
        internal_mapper = {}
        internal_mapper = {item["value"]: item["count"] for item in internal_result.items}
        # 外网可访问的
        for vuln_label in self._vuln_label:
            result.append(
                {
                    "label": vuln_label,
                    "number": internal_mapper.get(vuln_label, 0),
                    "percentage": f"{round((internal_mapper.get(vuln_label, 0) / count)*100)}%" if count != 0 else "0%",
                    "internal": True,
                }
            )
        external_asql = "$.internet_mapping.exposure = '外网可访问'"
        external_result = query_filter_manager.calculate_attribute_filter(
            external_asql,
            Category.VUL_INSTANCE_UNIQUE,
            req.date,
            venn_filter_entity=req.venn_filter,
            attribute_filter_entity=req.attribute_filter,
            entity_type=req.entity_type,
            init=False,
            offset=req.page_size * req.page_index,
            limit=req.page_size,
            query_filter_id=req.query_filter_id,
        )
        external_mapper = {item["value"]: item["count"] for item in external_result.items}
        for vuln_label in self._vuln_label:
            result.append(
                {
                    "label": vuln_label,
                    "number": external_mapper.get(vuln_label, 0),
                    "percentage": f"{round((external_mapper.get(vuln_label, 0) / count)*100)}%" if count != 0 else "0%",
                    "internal": False,
                }
            )
        return build_success(data=result)


class VulnInstanceAggeWithVulnNameAPIView(APIView, BaseQuery):
    def post(self, request: Request):
        serializer = VulnInstanceAggeWithVulnNameRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(code=ResponseCode.REQUEST_ERROR)
        req: VulnAggsWithVulnNameRequest = serializer.save()
        global_asql = self._get_asql(req.asql, req.category, req.date, req.filters, req.entity_type)
        querier = ESLogicalGroupQuerier(self.find_field_to_mapper(Category.VUL_INSTANCE_UNIQUE, None))
        global_condition, _, _ = querier.parse_aql(Category.VUL_INSTANCE_UNIQUE, global_asql)
        aggs = {
            "composite_buckets": {
                "composite": {
                    "size": 10000,
                    "sources": [
                        {"cve_id": {"terms": {"field": "vul_instance_unique.cve_id"}}},
                        {"name": {"terms": {"field": "vul_instance_unique.name"}}},
                    ],
                },
            }
        }
        count = entity_service.composite_count(
            Category.VUL_INSTANCE_UNIQUE,
            None,
            condition=global_condition,
            aggs=aggs,
            fields=req.fields,
        )
        pre_size = req.page_index * req.page_size
        if pre_size >= count:
            return build_success(data={"count": count, "data": []})
        aggs["composite_buckets"]["composite"]["size"] = req.page_size
        data = entity_service.aggr_search(
            category=Category.VUL_INSTANCE_UNIQUE,
            date=None,
            query=global_condition,
            aggs=aggs,
            fields=req.fields,
        )
        after_key = data.get("aggregations", {}).get("composite_buckets", {}).get("after_key", None)
        if after_key:
            aggs["composite_buckets"]["composite"]["after"] = after_key
        aggs["composite_buckets"]["aggs"] = {
            "rel_ids": {
                "terms": {"field": "vul_instance_unique.asset.rel_id", "size": 10000},
            },
            "adapters": {"terms": {"field": "base.adapters", "size": 10000}},
        }
        res = entity_service.aggr_search(
            category=Category.VUL_INSTANCE_UNIQUE,
            date=None,
            query=global_condition,
            aggs=aggs,
            fields=req.fields,
        )
        result = self.paser_aggs(res)
        return build_success(data={"count": count, "data": result})

    def paser_aggs(self, data):
        result = []
        for bucket in data.get("aggregations", {}).get("composite_buckets", {}).get("buckets", []):
            result.append(
                {
                    "cve_id": bucket.get("key", {}).get("cve_id", ""),
                    "name": bucket.get("key", {}).get("name", ""),
                    "rel_ids": list(set([item.get("key") for item in bucket.get("rel_ids", {}).get("buckets", [])])),
                    "adapters": list(set([item.get("key") for item in bucket.get("adapters", {}).get("buckets", [])])),
                }
            )
        return result


class VulnInstanceCVSSAPIView(APIView):
    def get(self, request: Request):
        name = request.query_params.get("name", None)
        if not name:
            return build_failed(code=ResponseCode.REQUEST_ERROR)
        f_path = caasm_config.ROOT_DIR / "caasm_script" / "data" / "view" / "vulnerbility" / f"{name}.yml"
        if not os.path.exists(f_path):
            return build_failed(code=ResponseCode.REQUEST_ERROR)
        with open(f_path, "r", encoding="utf-8") as f:
            data = yaml.safe_load(f.read())
            return build_success(data=data)
        return build_failed(code=ResponseCode.REQUEST_ERROR)


class VulnTotalViewAPIView(APIView, BaseQuery):
    def get(self, request: Request):
        result = {}
        querier = ESLogicalGroupQuerier(self.find_field_to_mapper(Category.VUL_INSTANCE_UNIQUE, None))

        # TODO 漏洞采集统计
        vuln_febric = []
        # 漏洞融合
        vuln_count = entity_service.get_count(category=Category.VUL_INSTANCE_UNIQUE, date=None)
        vuln_febric.append({"label": "漏洞融合", "value": vuln_count})
        external_condition, _, _ = querier.parse_aql(
            Category.VUL_INSTANCE_UNIQUE, "$.internet_mapping.exposure = '外网可访问'; $.change_base.status = '新增'"
        )
        external_append_count = entity_service.get_count(
            category=Category.VUL_INSTANCE_UNIQUE,
            date=None,
            condition=external_condition,
        )
        vuln_febric.append({"label": "新增外网漏洞", "value": external_append_count})
        internal_condition, _, _ = querier.parse_aql(
            Category.VUL_INSTANCE_UNIQUE, "$.internet_mapping.exposure = '内网可访问'; $.change_base.status = '新增'"
        )
        internal_append_count = entity_service.get_count(
            category=Category.VUL_INSTANCE_UNIQUE,
            date=None,
            condition=internal_condition,
        )
        vuln_febric.append({"label": "新增内网漏洞", "value": internal_append_count})
        # 融合贡献度
        adapters_result = query_filter_manager.calculate_attribute_filter(
            "",
            Category.VUL_INSTANCE_UNIQUE,
            None,
            venn_filter_entity=None,
            attribute_filter_entity=QueryFilterEntity(
                title="漏洞严重程度",
                type="attribute_filter",
                config={"field": "base.adapters", "selectAll": False},
                filter_id="QueryFilterType.ATTRIBUTE:base.adapters",
                selected_items=[],
                items=[],
            ),
            entity_type=None,
            init=False,
            offset=0,
            limit=100,
            query_filter_id=None,
        )
        vuln_febric.append({"label": "融合贡献度", "value": adapters_result.items})
        result["vuln_fabric"] = vuln_febric

        # 漏洞优先级
        vuln_priority = []
        priority_condition, _, _ = querier.parse_aql(
            Category.VUL_INSTANCE_UNIQUE, "$.vul_instance_unique.priority.exists()"
        )
        vuln_priority_count = entity_service.get_count(
            category=Category.VUL_INSTANCE_UNIQUE,
            date=None,
            condition=priority_condition,
        )
        vuln_priority.append({"label": "漏洞优先级", "value": vuln_priority_count})
        priority_result = query_filter_manager.calculate_attribute_filter(
            "",
            Category.VUL_INSTANCE_UNIQUE,
            None,
            venn_filter_entity=None,
            attribute_filter_entity=QueryFilterEntity(
                title="漏洞优先级",
                type="attribute_filter",
                config={"field": "vul_instance_unique.priority", "selectAll": False},
                filter_id="QueryFilterType.ATTRIBUTE:vul_instance_unique.priority",
                selected_items=[],
                items=[],
            ),
            entity_type=None,
            init=False,
            offset=0,
            limit=10,
            query_filter_id=None,
        )
        vuln_priority.append({"label": "漏洞优先级分布", "value": priority_result.items})
        # 紧急漏洞贡献度
        priority_result = query_filter_manager.calculate_attribute_filter(
            "$.vul_instance_unique.priority = '致命'",
            Category.VUL_INSTANCE_UNIQUE,
            None,
            venn_filter_entity=None,
            attribute_filter_entity=QueryFilterEntity(
                title="适配器贡献度",
                type="attribute_filter",
                config={"field": "base.adapters", "selectAll": False},
                filter_id="QueryFilterType.ATTRIBUTE:base.adapters",
                selected_items=[],
                items=[],
            ),
            entity_type=None,
            init=False,
            offset=0,
            limit=10,
            query_filter_id=None,
        )
        vuln_priority.append({"label": "漏洞紧急贡献度", "value": priority_result.items})
        # 今日新增优先级漏洞
        priority_result = query_filter_manager.calculate_attribute_filter(
            "$.change_base.status = '新增'",
            Category.VUL_INSTANCE_UNIQUE,
            None,
            venn_filter_entity=None,
            attribute_filter_entity=QueryFilterEntity(
                title="漏洞优先级",
                type="attribute_filter",
                config={"field": "vul_instance_unique.priority", "selectAll": False},
                filter_id="QueryFilterType.ATTRIBUTE:vul_instance_unique.priority",
                selected_items=[],
                items=[],
            ),
            entity_type=None,
            init=False,
            offset=0,
            limit=10,
            query_filter_id=None,
        )
        priority_count = 0
        for item in priority_result.items:
            priority_count += item["count"]
        for item in priority_result.items:
            item["percentage"] = f"{round((item['count'] / priority_count)*100)}%" if priority_count != 0 else "0%"
        vuln_priority.append({"label": "今日新增优先级漏洞", "value": priority_result.items})
        result["vuln_priority"] = vuln_priority
        return build_success(data=result)


class vulnDistributionViewAPIView(APIView, BaseQuery):
    def get(self, request: Request):
        result = {}
        querier = ESLogicalGroupQuerier(self.find_field_to_mapper(Category.VUL_INSTANCE_UNIQUE, None))
        # 漏洞分布
        vuln_distribution = []
        # 漏洞数量
        external_vuln = query_filter_manager.calculate_attribute_filter(
            "$.internet_mapping.exposure = '外网可访问'",
            Category.VUL_INSTANCE_UNIQUE,
            None,
            venn_filter_entity=None,
            attribute_filter_entity=QueryFilterEntity(
                title="漏洞状态",
                type="attribute_filter",
                config={"field": "vul_instance_unique.response_status", "selectAll": False},
                filter_id="QueryFilterType.ATTRIBUTE:vul_instance_unique.response_status",
                selected_items=[],
                items=[],
            ),
            entity_type=None,
            init=False,
            offset=0,
            limit=10,
            query_filter_id=None,
        )
        internal_vuln = query_filter_manager.calculate_attribute_filter(
            "$.internet_mapping.exposure = '内网可访问'",
            Category.VUL_INSTANCE_UNIQUE,
            None,
            venn_filter_entity=None,
            attribute_filter_entity=QueryFilterEntity(
                title="漏洞状态",
                type="attribute_filter",
                config={"field": "vul_instance_unique.response_status", "selectAll": False},
                filter_id="QueryFilterType.ATTRIBUTE:vul_instance_unique.response_status",
                selected_items=[],
                items=[],
            ),
            entity_type=None,
            init=False,
            offset=0,
            limit=10,
            query_filter_id=None,
        )
        return build_success(data={"external_vuln": external_vuln.items, "internal_vuln": internal_vuln.items})


class VulnPriorityTrendAPIView(APIView):
    def post(self, request: Request):
        serializer = VulnPriorityTrendRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return build_failed(code=ResponseCode.REQUEST_ERROR)

        req: VulnPriorityTrendRequest = serializer.save()
        date_filter_cond = {}
        # 构建时间范围查询条件
        if req.start_time:
            start_time_str = req.start_time.strftime(DATETIME_FORMAT)
            date_filter_cond["gte"] = start_time_str
        if req.end_time:
            end_time_str = req.end_time.strftime(DATETIME_FORMAT)
            date_filter_cond["lte"] = end_time_str

        # 构建时间范围查询条件
        if date_filter_cond:
            time_range_condition = {"bool": {"must": [{"range": {"asset_base.updated_on": date_filter_cond}}]}}
        else:
            time_range_condition = None
        # 构建聚合查询，按日期和优先级聚合
        aggs = {
            "daily_priority_distribution": {
                "date_histogram": {
                    "field": "asset_base.updated_on",
                    "calendar_interval": "1d",
                    "format": "yyyy-MM-dd",
                    "min_doc_count": 0,
                },
                "aggs": {
                    "priority_distribution": {
                        "terms": {"field": "vul_instance_unique.priority", "size": 10, "missing": "未知"}
                    }
                },
            }
        }

        # 执行聚合查询
        result = entity_service.aggr_search(
            category=Category.VUL_INSTANCE_UNIQUE, date=None, query=time_range_condition, aggs=aggs, size=0
        )

        # 解析聚合结果
        trend_data = []
        aggregations = result.get("aggregations", {})
        daily_buckets = aggregations.get("daily_priority_distribution", {}).get("buckets", [])

        for daily_bucket in daily_buckets:
            date = daily_bucket["key_as_string"]
            priority_buckets = daily_bucket.get("priority_distribution", {}).get("buckets", [])

            priority_distribution = {}
            for priority_bucket in priority_buckets:
                priority = priority_bucket["key"]
                count = priority_bucket["doc_count"]
                priority_distribution[priority] = count

            trend_data.append(
                {"date": date, "priority_distribution": priority_distribution, "total_count": daily_bucket["doc_count"]}
            )

        return build_success(data=trend_data)


class VulnTraitsStatisticsAPIView(APIView):
    """统计 vul_instance_unique.traits 字段值的接口，返回数量最大的前10个"""

    def get(self, request: Request):
        # 构建聚合查询，统计 traits 字段的值
        aggs = {
            "traits_statistics": {
                "terms": {
                    "field": "vul_instance_unique.traits",
                    "size": 10,  # 返回前10个
                    "order": {"_count": "desc"},  # 按数量降序排列
                }
            }
        }

        # 执行聚合查询
        result = entity_service.aggr_search(
            category=Category.VUL_INSTANCE_UNIQUE,
            date=None,
            query=None,
            aggs=aggs,
            size=0,
        )

        # 解析聚合结果
        traits_data = []
        aggregations = result.get("aggregations", {})
        traits_buckets = aggregations.get("traits_statistics", {}).get("buckets", [])

        for bucket in traits_buckets:
            trait_value = bucket["key"]
            count = bucket["doc_count"]
            traits_data.append({"trait": trait_value, "count": count})

        return build_success(data=traits_data)


class VulnAssetDisplayValueStatisticsAPIView(APIView):
    """统计 vul_instance_unique.asset.display_value 字段值的接口，返回数量最大的前10个"""

    def get(self, request: Request):
        # 构建聚合查询，统计 asset.display_value 字段的值
        aggs = {
            "asset_ip": {
                "terms": {
                    "field": "vul_instance_unique.asset.display_value",
                    "size": 10,  # 返回前10个
                    "order": {"_count": "desc"},  # 按数量降序排列
                }
            }
        }

        # 执行聚合查询
        result = entity_service.aggr_search(
            category=Category.VUL_INSTANCE_UNIQUE,
            date=None,
            query=None,
            aggs=aggs,
            size=0,
        )

        # 解析聚合结果
        asset_data = []
        aggregations = result.get("aggregations", {})
        asset_buckets = aggregations.get("asset_ip", {}).get("buckets", [])

        for bucket in asset_buckets:
            asset_display_value = bucket["key"]
            count = bucket["doc_count"]
            asset_data.append({"asset_ip": asset_display_value, "count": count})

        return build_success(data=asset_data)


class VulnPackageNameStatisticsAPIView(APIView):
    """统计 vul_instance_unique.package.name 字段值的接口，返回数量最大的前10个"""

    def get(self, request: Request):
        # 构建聚合查询，统计 package.name 字段的值
        aggs = {
            "package_name_statistics": {
                "terms": {
                    "field": "vul_instance_unique.package.name",
                    "size": 10,  # 返回前10个
                    "order": {"_count": "desc"},  # 按数量降序排列
                }
            }
        }

        # 执行聚合查询
        result = entity_service.aggr_search(
            category=Category.VUL_INSTANCE_UNIQUE,
            date=None,
            query=None,
            aggs=aggs,
            size=0,
        )

        # 解析聚合结果
        package_data = []
        aggregations = result.get("aggregations", {})
        package_buckets = aggregations.get("package_name_statistics", {}).get("buckets", [])

        for bucket in package_buckets:
            package_name = bucket["key"]
            count = bucket["doc_count"]
            package_data.append({"package_name": package_name, "count": count})

        return build_success(data=package_data)


class VulnExternalAssetStatisticsAPIView(APIView, BaseQuery):
    """统计外网可访问的 vul_instance_unique.asset.display_value 字段值的接口，返回数量最大的前10个"""

    def get(self, request: Request):
        # 构建查询条件：外网可访问
        querier = ESLogicalGroupQuerier(self.find_field_to_mapper(Category.VUL_INSTANCE_UNIQUE, None))
        external_asql = "$.internet_mapping.exposure = '外网可访问'"
        external_condition, _, _ = querier.parse_aql(Category.VUL_INSTANCE_UNIQUE, external_asql)

        # 构建聚合查询，统计 asset.display_value 字段的值
        aggs = {
            "external_asset_display_value_statistics": {
                "terms": {
                    "field": "vul_instance_unique.asset.display_value",
                    "size": 10,  # 返回前10个
                    "order": {"_count": "desc"},  # 按数量降序排列
                }
            }
        }

        # 执行聚合查询
        result = entity_service.aggr_search(
            category=Category.VUL_INSTANCE_UNIQUE,
            date=None,
            query=external_condition,
            aggs=aggs,
            size=0,
        )

        # 解析聚合结果
        asset_data = []
        aggregations = result.get("aggregations", {})
        asset_buckets = aggregations.get("external_asset_display_value_statistics", {}).get("buckets", [])

        for bucket in asset_buckets:
            asset_display_value = bucket["key"]
            count = bucket["doc_count"]
            asset_data.append({"asset_display_value": asset_display_value, "count": count})

        return build_success(data=asset_data)
