"""
测试资产显示值统计接口
"""
import pytest
from unittest.mock import patch, MagicMock
from unittest import mock
from rest_framework.test import APIClient
from rest_framework import status

from caasm_webapi.app.vuln_instance.views import (
    VulnAssetDisplayValueStatisticsAPIView,
    VulnPackageNameStatisticsAPIView,
    VulnExternalAssetStatisticsAPIView,
    VulnBusinessSystemStatisticsAPIView
)


class TestVulnAssetDisplayValueStatisticsAPIView:
    """测试VulnAssetDisplayValueStatisticsAPIView接口"""

    def setup_method(self):
        """设置测试环境"""
        self.client = APIClient()
        self.view = VulnAssetDisplayValueStatisticsAPIView()

    @patch('caasm_webapi.app.vuln_instance.views.entity_service')
    def test_get_asset_display_value_statistics_success(self, mock_entity_service):
        """测试成功获取资产显示值统计"""
        # 模拟聚合查询结果
        mock_result = {
            "aggregations": {
                "asset_display_value_statistics": {
                    "buckets": [
                        {"key": "192.168.1.100", "doc_count": 15},
                        {"key": "192.168.1.101", "doc_count": 12},
                        {"key": "192.168.1.102", "doc_count": 8},
                        {"key": "web-server-01", "doc_count": 6},
                        {"key": "db-server-01", "doc_count": 4},
                    ]
                }
            }
        }
        mock_entity_service.aggr_search.return_value = mock_result

        # 发送GET请求
        response = self.client.get('/api/vul_instance_unique/assetDisplayValueStatistics/')

        # 验证响应
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        
        # 验证响应结构
        assert 'code' in response_data
        assert 'data' in response_data
        assert response_data['code'] == 200
        
        # 验证数据内容
        data = response_data['data']
        assert len(data) == 5
        
        # 验证第一个结果
        assert data[0]['asset_display_value'] == '192.168.1.100'
        assert data[0]['count'] == 15
        
        # 验证第二个结果
        assert data[1]['asset_display_value'] == '192.168.1.101'
        assert data[1]['count'] == 12

        # 验证entity_service.aggr_search被正确调用
        mock_entity_service.aggr_search.assert_called_once()
        call_args = mock_entity_service.aggr_search.call_args
        
        # 验证调用参数
        assert call_args[1]['size'] == 0
        assert 'asset_display_value_statistics' in call_args[1]['aggs']
        assert call_args[1]['aggs']['asset_display_value_statistics']['terms']['field'] == 'vul_instance_unique.asset.display_value'
        assert call_args[1]['aggs']['asset_display_value_statistics']['terms']['size'] == 10
        assert call_args[1]['aggs']['asset_display_value_statistics']['terms']['order'] == {'_count': 'desc'}

    @patch('caasm_webapi.app.vuln_instance.views.entity_service')
    def test_get_asset_display_value_statistics_empty_result(self, mock_entity_service):
        """测试空结果的情况"""
        # 模拟空的聚合查询结果
        mock_result = {
            "aggregations": {
                "asset_display_value_statistics": {
                    "buckets": []
                }
            }
        }
        mock_entity_service.aggr_search.return_value = mock_result

        # 发送GET请求
        response = self.client.get('/api/vul_instance_unique/assetDisplayValueStatistics/')

        # 验证响应
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        
        # 验证响应结构
        assert response_data['code'] == 200
        assert response_data['data'] == []

    @patch('caasm_webapi.app.vuln_instance.views.entity_service')
    def test_get_asset_display_value_statistics_missing_aggregations(self, mock_entity_service):
        """测试缺少聚合结果的情况"""
        # 模拟缺少聚合结果的情况
        mock_result = {}
        mock_entity_service.aggr_search.return_value = mock_result

        # 发送GET请求
        response = self.client.get('/api/vul_instance_unique/assetDisplayValueStatistics/')

        # 验证响应
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        
        # 验证响应结构
        assert response_data['code'] == 200
        assert response_data['data'] == []

    def test_view_class_attributes(self):
        """测试视图类的基本属性"""
        # 验证类文档字符串
        assert VulnAssetDisplayValueStatisticsAPIView.__doc__ == "统计 vul_instance_unique.asset.display_value 字段值的接口，返回数量最大的前10个"

        # 验证类继承
        from rest_framework.views import APIView
        assert issubclass(VulnAssetDisplayValueStatisticsAPIView, APIView)

        # 验证方法存在
        assert hasattr(VulnAssetDisplayValueStatisticsAPIView, 'get')


class TestVulnPackageNameStatisticsAPIView:
    """测试VulnPackageNameStatisticsAPIView接口"""

    def setup_method(self):
        """设置测试环境"""
        self.client = APIClient()
        self.view = VulnPackageNameStatisticsAPIView()

    @patch('caasm_webapi.app.vuln_instance.views.entity_service')
    def test_get_package_name_statistics_success(self, mock_entity_service):
        """测试成功获取软件包名称统计"""
        # 模拟聚合查询结果
        mock_result = {
            "aggregations": {
                "package_name_statistics": {
                    "buckets": [
                        {"key": "nginx", "doc_count": 25},
                        {"key": "apache", "doc_count": 18},
                        {"key": "mysql", "doc_count": 15},
                        {"key": "redis", "doc_count": 12},
                        {"key": "postgresql", "doc_count": 8},
                    ]
                }
            }
        }
        mock_entity_service.aggr_search.return_value = mock_result

        # 发送GET请求
        response = self.client.get('/api/vul_instance_unique/packageNameStatistics/')

        # 验证响应
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        # 验证响应结构
        assert 'code' in response_data
        assert 'data' in response_data
        assert response_data['code'] == 200

        # 验证数据内容
        data = response_data['data']
        assert len(data) == 5

        # 验证第一个结果
        assert data[0]['package_name'] == 'nginx'
        assert data[0]['count'] == 25

        # 验证第二个结果
        assert data[1]['package_name'] == 'apache'
        assert data[1]['count'] == 18

        # 验证entity_service.aggr_search被正确调用
        mock_entity_service.aggr_search.assert_called_once()
        call_args = mock_entity_service.aggr_search.call_args

        # 验证调用参数
        assert call_args[1]['size'] == 0
        assert 'package_name_statistics' in call_args[1]['aggs']
        assert call_args[1]['aggs']['package_name_statistics']['terms']['field'] == 'vul_instance_unique.package.name'
        assert call_args[1]['aggs']['package_name_statistics']['terms']['size'] == 10
        assert call_args[1]['aggs']['package_name_statistics']['terms']['order'] == {'_count': 'desc'}

    @patch('caasm_webapi.app.vuln_instance.views.entity_service')
    def test_get_package_name_statistics_empty_result(self, mock_entity_service):
        """测试空结果的情况"""
        # 模拟空的聚合查询结果
        mock_result = {
            "aggregations": {
                "package_name_statistics": {
                    "buckets": []
                }
            }
        }
        mock_entity_service.aggr_search.return_value = mock_result

        # 发送GET请求
        response = self.client.get('/api/vul_instance_unique/packageNameStatistics/')

        # 验证响应
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        # 验证响应结构
        assert response_data['code'] == 200
        assert response_data['data'] == []

    def test_package_name_view_class_attributes(self):
        """测试软件包名称统计视图类的基本属性"""
        # 验证类文档字符串
        assert VulnPackageNameStatisticsAPIView.__doc__ == "统计 vul_instance_unique.package.name 字段值的接口，返回数量最大的前10个"

        # 验证类继承
        from rest_framework.views import APIView
        assert issubclass(VulnPackageNameStatisticsAPIView, APIView)

        # 验证方法存在
        assert hasattr(VulnPackageNameStatisticsAPIView, 'get')


class TestVulnExternalAssetDisplayValueStatisticsAPIView:
    """测试VulnExternalAssetDisplayValueStatisticsAPIView接口"""

    def setup_method(self):
        """设置测试环境"""
        self.client = APIClient()
        self.view = VulnExternalAssetDisplayValueStatisticsAPIView()

    @patch('caasm_webapi.app.vuln_instance.views.entity_service')
    @patch('caasm_webapi.app.vuln_instance.views.ESLogicalGroupQuerier')
    def test_get_external_asset_statistics_success(self, mock_querier_class, mock_entity_service):
        """测试成功获取外网资产统计"""
        # 模拟查询器
        mock_querier = mock_querier_class.return_value
        mock_querier.parse_aql.return_value = ({"bool": {"must": [{"term": {"internet_mapping.exposure": "外网可访问"}}]}}, None, None)

        # 模拟聚合查询结果
        mock_result = {
            "aggregations": {
                "external_asset_display_value_statistics": {
                    "buckets": [
                        {"key": "203.0.113.10", "doc_count": 20},
                        {"key": "203.0.113.11", "doc_count": 15},
                        {"key": "203.0.113.12", "doc_count": 12},
                        {"key": "external-web-01", "doc_count": 8},
                        {"key": "external-api-01", "doc_count": 5},
                    ]
                }
            }
        }
        mock_entity_service.aggr_search.return_value = mock_result

        # 发送GET请求
        response = self.client.get('/api/vul_instance_unique/externalAssetStatistics/')

        # 验证响应
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        # 验证响应结构
        assert 'code' in response_data
        assert 'data' in response_data
        assert response_data['code'] == 200

        # 验证数据内容
        data = response_data['data']
        assert len(data) == 5

        # 验证第一个结果
        assert data[0]['asset_display_value'] == '203.0.113.10'
        assert data[0]['count'] == 20

        # 验证第二个结果
        assert data[1]['asset_display_value'] == '203.0.113.11'
        assert data[1]['count'] == 15

        # 验证查询器被正确调用
        mock_querier.parse_aql.assert_called_once_with(
            mock.ANY,  # Category.VUL_INSTANCE_UNIQUE
            "$.internet_mapping.exposure = '外网可访问'"
        )

        # 验证entity_service.aggr_search被正确调用
        mock_entity_service.aggr_search.assert_called_once()
        call_args = mock_entity_service.aggr_search.call_args

        # 验证调用参数
        assert call_args[1]['size'] == 0
        assert 'external_asset_display_value_statistics' in call_args[1]['aggs']
        assert call_args[1]['aggs']['external_asset_display_value_statistics']['terms']['field'] == 'vul_instance_unique.asset.display_value'

    def test_external_asset_view_class_attributes(self):
        """测试外网资产统计视图类的基本属性"""
        # 验证类文档字符串
        expected_doc = "统计外网可访问的 vul_instance_unique.asset.display_value 字段值的接口，返回数量最大的前10个"
        assert VulnExternalAssetDisplayValueStatisticsAPIView.__doc__ == expected_doc

        # 验证类继承
        from rest_framework.views import APIView
        from caasm_render.query.base import BaseQuery
        assert issubclass(VulnExternalAssetDisplayValueStatisticsAPIView, APIView)
        assert issubclass(VulnExternalAssetDisplayValueStatisticsAPIView, BaseQuery)

        # 验证方法存在
        assert hasattr(VulnExternalAssetStatisticsAPIView, 'get')


class TestVulnBusinessSystemStatisticsAPIView:
    """测试VulnBusinessSystemStatisticsAPIView接口"""

    def setup_method(self):
        """设置测试环境"""
        self.client = APIClient()
        self.view = VulnBusinessSystemStatisticsAPIView()

    @patch('caasm_webapi.app.vuln_instance.views.entity_service')
    def test_get_business_system_statistics_success(self, mock_entity_service):
        """测试成功获取业务系统统计"""
        # 模拟聚合查询结果
        mock_result = {
            "aggregations": {
                "business_system_statistics": {
                    "buckets": [
                        {"key": "电商平台", "doc_count": 45},
                        {"key": "支付系统", "doc_count": 32},
                        {"key": "用户管理系统", "doc_count": 28},
                        {"key": "订单管理系统", "doc_count": 21},
                        {"key": "库存管理系统", "doc_count": 15},
                    ]
                }
            }
        }
        mock_entity_service.aggr_search.return_value = mock_result

        # 发送GET请求
        response = self.client.get('/api/vul_instance_unique/businessSystemStatistics/')

        # 验证响应
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        # 验证响应结构
        assert 'code' in response_data
        assert 'data' in response_data
        assert response_data['code'] == 200

        # 验证数据内容
        data = response_data['data']
        assert len(data) == 5

        # 验证第一个结果
        assert data[0]['business_name'] == '电商平台'
        assert data[0]['count'] == 45

        # 验证第二个结果
        assert data[1]['business_name'] == '支付系统'
        assert data[1]['count'] == 32

        # 验证entity_service.aggr_search被正确调用
        mock_entity_service.aggr_search.assert_called_once()
        call_args = mock_entity_service.aggr_search.call_args

        # 验证调用参数
        assert call_args[1]['size'] == 0
        assert 'business_system_statistics' in call_args[1]['aggs']
        assert call_args[1]['aggs']['business_system_statistics']['terms']['field'] == 'asset_base.ownership.business.name'
        assert call_args[1]['aggs']['business_system_statistics']['terms']['size'] == 10
        assert call_args[1]['aggs']['business_system_statistics']['terms']['order'] == {'_count': 'desc'}

    @patch('caasm_webapi.app.vuln_instance.views.entity_service')
    def test_get_business_system_statistics_empty_result(self, mock_entity_service):
        """测试空结果的情况"""
        # 模拟空的聚合查询结果
        mock_result = {
            "aggregations": {
                "business_system_statistics": {
                    "buckets": []
                }
            }
        }
        mock_entity_service.aggr_search.return_value = mock_result

        # 发送GET请求
        response = self.client.get('/api/vul_instance_unique/businessSystemStatistics/')

        # 验证响应
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        # 验证响应结构
        assert response_data['code'] == 200
        assert response_data['data'] == []

    def test_business_system_view_class_attributes(self):
        """测试业务系统统计视图类的基本属性"""
        # 验证类文档字符串
        expected_doc = "统计业务系统漏洞数据的接口，聚合字段为 asset_base.ownership.business.name，返回数量最大的前10个"
        assert VulnBusinessSystemStatisticsAPIView.__doc__ == expected_doc

        # 验证类继承
        from rest_framework.views import APIView
        assert issubclass(VulnBusinessSystemStatisticsAPIView, APIView)

        # 验证方法存在
        assert hasattr(VulnBusinessSystemStatisticsAPIView, 'get')
